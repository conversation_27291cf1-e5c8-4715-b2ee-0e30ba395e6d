'use client';

import React, { useState, useEffect } from 'react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { TokenPurchaseModal } from '@/components/tokens/TokenPurchaseModal';
import {
  Coins,
  CreditCard,
  History,
  Gift,
  Plus,
  RefreshCw
} from 'lucide-react';

interface TokenPackage {
  id: string;
  tokens: number;
  priceKES: number;
  priceUSD: number;
  bonus: number;
  popular?: boolean;
  description: string;
}

interface Transaction {
  _id: string;
  type: 'purchase' | 'earned' | 'spent';
  amount: number;
  description: string;
  createdAt: string;
  status: 'completed' | 'pending' | 'failed';
}

interface User {
  id: string;
  phoneNumber: string;
  email?: string;
  firstName: string;
  lastName: string;
  fullName: string;
  profilePicture?: string;
  location: {
    county: string;
    town: string;
  };
  isVerified: boolean;
  role: string;
  pediTokens: number;
  rating: number;
  sustainabilityScore: number;
  totalDonations: number;
  totalSwaps: number;
  preferences: Record<string, unknown>;
  joinedAt: string;
  lastActive: string;
}

const tokenPackages: TokenPackage[] = [
  {
    id: 'starter',
    tokens: 100,
    priceKES: 500,
    priceUSD: 5,
    bonus: 0,
    description: 'Perfect for getting started'
  },
  {
    id: 'popular',
    tokens: 250,
    priceKES: 1200,
    priceUSD: 12,
    bonus: 25,
    popular: true,
    description: 'Most popular choice'
  },
  {
    id: 'value',
    tokens: 500,
    priceKES: 2300,
    priceUSD: 23,
    bonus: 75,
    description: 'Best value for money'
  },
  {
    id: 'premium',
    tokens: 1000,
    priceKES: 4500,
    priceUSD: 45,
    bonus: 200,
    description: 'For power users'
  }
];

export default function TokensPage() {
  const [user, setUser] = useState<User | null>(null);

  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [customAmount, setCustomAmount] = useState('');

  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);


  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }

    fetchTransactions();
  }, []);

  const refreshUserData = async () => {
    setRefreshing(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/users/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.data);
        localStorage.setItem('user', JSON.stringify(data.data));
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handlePurchaseSuccess = () => {
    // Refresh user data to get updated token balance
    refreshUserData();
    // Refresh transaction history
    fetchTransactions();
  };

  const fetchTransactions = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/tokens/history', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTransactions(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
    }
  };



  const calculateCustomTokens = (amount: string) => {
    const kes = parseFloat(amount);
    if (isNaN(kes) || kes <= 0) return 0;
    return Math.floor(kes / 5); // 5 KES per token
  };

  if (!user) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Pedi Tokens</h1>
            <p className="text-gray-600">Purchase tokens to exchange for clothing items</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="flex items-center gap-2">
                <Coins className="h-5 w-5 text-yellow-600" />
                <span className="text-2xl font-bold text-yellow-600">{user.pediTokens}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={refreshUserData}
                  disabled={refreshing}
                  className="ml-2"
                >
                  <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <p className="text-sm text-gray-500">Current Balance</p>
            </div>
            <Button
              onClick={() => setShowPurchaseModal(true)}
              className="bg-[#01796F] hover:bg-[#032221] text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Buy Tokens
            </Button>
          </div>
        </div>

        <Tabs defaultValue="purchase" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="purchase">Purchase Tokens</TabsTrigger>
            <TabsTrigger value="history">Transaction History</TabsTrigger>
          </TabsList>

          <TabsContent value="purchase" className="space-y-6">
            {/* Token Packages */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {tokenPackages.map((pkg) => (
                <Card
                  key={pkg.id}
                  className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:border-[#01796F] ${pkg.popular ? 'relative' : ''}`}
                  onClick={() => setShowPurchaseModal(true)}
                >
                  {pkg.popular && (
                    <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-[#01796F]">
                      Most Popular
                    </Badge>
                  )}
                  
                  <CardHeader className="text-center">
                    <div className="flex items-center justify-center mb-2">
                      <Coins className="h-8 w-8 text-yellow-600" />
                    </div>
                    <CardTitle className="text-2xl">{pkg.tokens} Tokens</CardTitle>
                    {pkg.bonus > 0 && (
                      <Badge variant="secondary" className="mx-auto">
                        +{pkg.bonus} Bonus
                      </Badge>
                    )}
                  </CardHeader>
                  
                  <CardContent className="text-center space-y-3">
                    <div>
                      <div className="text-2xl font-bold text-[#032221]">
                        KES {pkg.priceKES.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        ${pkg.priceUSD}
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                    
                    <div className="text-xs text-gray-500">
                      {pkg.bonus > 0 ? (
                        <span>Total: {pkg.tokens + pkg.bonus} tokens</span>
                      ) : (
                        <span>{(pkg.priceKES / pkg.tokens).toFixed(1)} KES per token</span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Custom Amount */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gift className="h-5 w-5" />
                  Custom Amount
                </CardTitle>
                <CardDescription>
                  Purchase any amount of tokens (5 KES = 1 token)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Label htmlFor="customAmount">Amount (KES)</Label>
                    <Input
                      id="customAmount"
                      type="number"
                      min="25"
                      value={customAmount}
                      onChange={(e) => setCustomAmount(e.target.value)}
                      placeholder="e.g., 1000"
                    />
                  </div>
                  <div className="flex-1">
                    <Label>Tokens You&apos;ll Get</Label>
                    <div className="h-10 flex items-center px-3 bg-gray-50 rounded-md">
                      <Coins className="h-4 w-4 text-yellow-600 mr-2" />
                      <span className="font-medium">
                        {calculateCustomTokens(customAmount)} tokens
                      </span>
                    </div>
                  </div>
                </div>
                
                <Button
                  onClick={() => setShowPurchaseModal(true)}
                  disabled={calculateCustomTokens(customAmount) === 0}
                  variant="outline"
                  className="w-full"
                >
                  Select Custom Amount
                </Button>
              </CardContent>
            </Card>


          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  Transaction History
                </CardTitle>
              </CardHeader>
              <CardContent>
                {transactions.length === 0 ? (
                  <div className="text-center py-8">
                    <Coins className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No transactions yet</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {transactions.map((transaction) => (
                      <div key={transaction._id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-full ${
                            transaction.type === 'purchase' ? 'bg-blue-100' :
                            transaction.type === 'earned' ? 'bg-green-100' : 'bg-red-100'
                          }`}>
                            {transaction.type === 'purchase' ? (
                              <CreditCard className="h-4 w-4 text-blue-600" />
                            ) : transaction.type === 'earned' ? (
                              <Gift className="h-4 w-4 text-green-600" />
                            ) : (
                              <Coins className="h-4 w-4 text-red-600" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{transaction.description}</p>
                            <p className="text-sm text-gray-500">
                              {new Date(transaction.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`font-medium ${
                            transaction.type === 'spent' ? 'text-red-600' : 'text-green-600'
                          }`}>
                            {transaction.type === 'spent' ? '-' : '+'}{transaction.amount} tokens
                          </p>
                          <Badge variant={
                            transaction.status === 'completed' ? 'default' :
                            transaction.status === 'pending' ? 'secondary' : 'destructive'
                          }>
                            {transaction.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Token Purchase Modal */}
      <TokenPurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        onSuccess={handlePurchaseSuccess}
        userBalance={user.pediTokens}
      />
    </DashboardLayout>
  );
}
